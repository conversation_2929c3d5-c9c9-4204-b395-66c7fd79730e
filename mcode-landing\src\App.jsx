import React, { useEffect } from "react";
import AOS from "aos";
import "aos/dist/aos.css";
import "./App.css";

export default function App() {
  useEffect(() => {
    AOS.init();
  }, []);

  return (
    <div>
      {/* Background layers */}
      <img className="image-gradient" src="/gradient.png" alt="gradient" />
      <div className="layer-blur"></div>

      <main>
        <div className="container">
          {/* Header */}
          <header>
            <h1 data-aos="fade-down" data-aos-duration="1500" className="logo">
              MCODE
            </h1>

            <nav>
              <a data-aos="fade-down" data-aos-duration="1500" href="#">
                COMPANY
              </a>
              <a data-aos="fade-down" data-aos-duration="2000" href="#">
                COMPANY
              </a>
              <a data-aos="fade-down" data-aos-duration="2500" href="#">
                COMPANY
              </a>
              <a data-aos="fade-down" data-aos-duration="3000" href="#">
                COMPANY
              </a>
            </nav>

            <button
              data-aos="fade-down"
              data-aos-duration="1500"
              className="btn-signing"
            >
              SIGNING
            </button>
          </header>

          {/* Hero */}
          <div className="content">
            <div
              data-aos="fade-zoom-in"
              data-aos-easing="ease-in-back"
              data-aos-delay="300"
              data-aos-offset="0"
              data-aos-duration="1500"
            >
              <div className="tag-box">
                <div className="tag">INTRO</div>
              </div>

              <h1
                data-aos="fade-zoom-in"
                data-aos-easing="ease-in-back"
                data-aos-delay="300"
                data-aos-offset="0"
                data-aos-duration="2000"
              >
                EMAIL FOR <br />
                DEVELOPERS
              </h1>

              <p
                data-aos="fade-zoom-in"
                data-aos-easing="ease-in-back"
                data-aos-delay="300"
                data-aos-offset="0"
                data-aos-duration="2500"
                className="description"
              >
                the best way to reach ugfhdhtgh drhethtdg fgrfgr fgsf
              </p>

              <div
                data-aos="fade-zoom-in"
                data-aos-easing="ease-in-back"
                data-aos-delay="300"
                data-aos-offset="0"
                data-aos-duration="3000"
                className="button"
              >
                <a href="#" className="btn-get-started">
                  Documentation &gt;&gt;
                </a>
                <a href="#" className="btn-signing-main">
                  Get Started &gt;
                </a>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* 3D Spline */}
      <spline-viewer
        class="robort-3d"
        url="https://prod.spline.design/72vCem1jXB5ef0g1/scene.splinecode"
      ></spline-viewer>
    </div>
  );
}
