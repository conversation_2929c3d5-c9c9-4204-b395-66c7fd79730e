*{
  margin: 0;
  padding:0;
  box-sizing: border-box;
}

body{
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  background-color: black;
  color: #e7e7ee;
  min-height: 100vh;
  line-height: 1.5;
}

.image-gradient{
  position: absolute;
  top: 0;
  right: 0;
  opacity: 0.5;
  z-index: -1;
}

.layer-blur{
  height: 0;
  width: 30rem;
  position:absolute;
  top: 20%;
  right: 0;
  box-shadow: 0 0 700px 15px white;
  rotate: -30deg;
  z-index: -1;
}

.container{
  width: 100%;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  overflow: hidden;
}

header{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 5rem;
  z-index: 999;
}

header h1{
  margin: 0;
  font-size: 3rem;
  font-weight: 300;
}

nav{
  display: flex;
  align-items: center;
  gap: 3rem;
  margin-left: -5%;
}

nav a{
  font-size: 1rem;
  letter-spacing: 0.1rem;
  transition: color 0.2s ease;
  text-decoration: none;
  color: inherit;
}

nav a:hover{
  color: #a7a7a7;
}

.btn-signing{
  background-color: #a7a7a7;
  color: black;
  padding: 0.8rem 2rem;
  border-radius: 50px;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  transition: background-color 0.2s ease;
  cursor: pointer;
}

.btn-signing:hover{
  background-color: white;
}

main{
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-height: calc(90vh - 6rem);
}

.content{
  max-width: 40rem;
  margin-left: 10%;
  z-index: 999;
}

.tag-box{
  position: relative;
  width: 18rem;
  height: 2.5rem;
  border-radius: 50px;
  background: linear-gradient(to right, #656565,#7f42a7,#6600c5,#5300a0,#757575,#656565);
  background-size: 200%;
  animation: animationGradient 2.5s linear infinite;
  box-shadow: 0 0 15px rgba(255,255,255,0.3);
}

@keyframes animationGradient {
  to{ background-position: 200%; }
}

.tag-box .tag{
  position: absolute;           /* FIX: needed for 'inset' to work */
  inset: 3px 3px 3px 3px;
  background-color: black;
  border-radius: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: 0.5s ease;
  cursor: pointer;
}

.tag-box .tag:hover{
  color: #5300a0;
}

.content h1{
  font-size: 4rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  margin: 2rem 0;
  line-height: 1.2;
  text-shadow: 0 0 10px rgba(128,128,128, 0.418);
}

.description{
  font-size: 1.2rem;
  letter-spacing: 0.5rem;
  max-width: 35rem;
  color: gray;
}

.button{
  display:flex;
  gap: 1rem;
  margin-top: 3rem;
}

.btn-get-started{
  text-decoration: none;
  border: 1px solid #2a2a2a;
  padding: 0.7rem 1.2rem;
  border-radius: 50px;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  transition: background-color 0.2s ease;
}

.btn-get-started:hover{
  background-color: #1a1a1a;
}

.btn-signing-main{
  text-decoration: none;
  background-color: lightgray;
  color: black;
  padding: 0.6rem 2.5rem;
  font-size: 1.2rem;
  font-weight: 600;
  letter-spacing: 0.1em;
  transition: background-color 0.2s ease;  /* FIX: typo 'background-clor' */
}

.btn-signing-main:hover{
  background-color: gray;
}

.robort-3d{
  position: absolute;
  top: 0;
  right: -20%;
}

.hero {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 100vh;
  padding: 0 5%;
  background-color: #000;
  color: #fff;
}

.hero-text {
  max-width: 50%;
}

.hero-3d {
  width: 500px;
  height: 500px;
}
